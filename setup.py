from setuptools import setup, find_packages

setup(
    name="autoloop-validation",
    version="0.1.0",
    description="自动驾驶数据自闭环验证项目",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    python_requires=">=3.8",
    install_requires=[
        "pyyaml>=6.0",
        "boto3>=1.26.0",
        "loguru>=0.6.0",
        "click>=8.0.0",
    ],
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "mypy>=0.950",
        ]
    },
    entry_points={
        "console_scripts": [
            "alv=core.cli:main",
        ],
    },
)