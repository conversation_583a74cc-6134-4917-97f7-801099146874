"""模型训练器"""

import os
import subprocess
import re
from typing import Dict, Any, Optional, List
from pathlib import Path
from utils.logger import get_logger

logger = get_logger(__name__)


class Trainer:
    """模型训练器"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config

        # 从training配置中获取参数
        training_config = config.get("training", {})
        self.exp = training_config.get("exp", "")
        self.ckpt = training_config.get("ckpt", "")
        self.pretrained_model = training_config.get("pretrained_model", "")
        self.max_epoch = training_config.get("max_epoch", 4)
        self.batch_size_per_device = training_config.get("batch_size_per_device", 10)
        self.total_devices = training_config.get("total_devices", 1)

        # 从training配置中获取数据配置
        self.train_data = training_config.get("train_data", "")
        self.train_data_list = training_config.get("train_data_list", [])
        self.data_mode = training_config.get("data_mode", "qyjpg")
        self.replacement_ratio = training_config.get("replacement_ratio", 0.08)
    
    def train(self, dry_run: bool = False) -> Dict[str, Any]:
        """执行训练
        
        Args:
            dry_run: 是否只生成命令不执行
            
        Returns:
            训练结果
        """
        result = {
            "success": False,
            "command": [],
            "output": "",
            "error": "",
            "model_path": "",
            "metrics": {}
        }
        
        try:
            logger.info("开始模型训练")
            logger.info(f"实验配置: {self.exp}")
            logger.info(f"预训练模型: {self.pretrained_model}")
            logger.info(f"最大轮数: {self.max_epoch}")
            logger.info(f"批次大小: {self.batch_size_per_device}")
            logger.info(f"设备数量: {self.total_devices}")
            logger.info(f"训练数据: {self.train_data}")
            logger.info(f"训练数据列表: {self.train_data_list} (类型: {type(self.train_data_list)})")
            logger.info(f"数据模式: {self.data_mode}")
            logger.info(f"替换比例: {self.replacement_ratio}")

            # 步骤1: 修改 Perceptron 项目中的数据配置
            if not dry_run:
                self._update_perceptron_data_config()
            else:
                logger.info("Dry run模式，跳过数据配置更新")

            # 步骤2: 构建训练命令
            train_cmd = self._build_training_command()
            result["command"] = train_cmd

            logger.info(f"训练命令: {' '.join(train_cmd)}")

            if dry_run:
                logger.info("Dry run模式，不执行实际训练")
                result["success"] = True
                result["output"] = "Dry run - 训练命令已生成"
                return result

            # 获取模型输出目录
            model_output_dir = self._get_model_output_dir()
            logger.info(f"模型输出目录: {model_output_dir}")

            # 创建模型输出目录
            Path(model_output_dir).mkdir(parents=True, exist_ok=True)

            # 执行训练命令
            logger.info("开始执行训练命令...")
            logger.info("=" * 60)

            # 设置环境变量
            import os
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'

            # 直接输出到控制台，不捕获输出
            process = subprocess.run(
                train_cmd,
                env=env,
                timeout=7200  # 2小时超时
            )

            logger.info("=" * 60)

            if process.returncode == 0:
                result["success"] = True
                # 使用实际的模型输出目录
                result["model_path"] = model_output_dir
                result["exp_name"] = self._generate_exp_name()
                logger.info("训练成功完成")
            else:
                logger.error(f"训练命令执行失败，返回码: {process.returncode}")
                raise subprocess.CalledProcessError(process.returncode, train_cmd)

            logger.info("训练完成")
            logger.info(f"模型保存路径: {result['model_path']}")
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"训练失败: {e}")
        
        return result
    
    def _update_perceptron_data_config(self):
        """更新 Perceptron 项目中的数据配置"""
        try:
            # 查找 perceptron/data/det3d/source/hf_car_9_tos.py 文件
            perceptron_data_file = Path("perceptron/data/det3d/source/hf_car_9_tos.py")

            if not perceptron_data_file.exists():
                logger.warning(f"Perceptron 数据配置文件不存在: {perceptron_data_file}")
                logger.info("跳过数据配置更新")
                return

            logger.info(f"更新 Perceptron 数据配置文件: {perceptron_data_file}")

            # 读取文件内容
            with open(perceptron_data_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 准备要添加的数据配置
            # train_data_list 可能是字符串或数组格式，原样处理
            if isinstance(self.train_data_list, str):
                # 如果是字符串，直接使用
                data_value = f'"{self.train_data_list}"'
            elif isinstance(self.train_data_list, list):
                data_list_str = ', '.join([f'"{item}"' for item in self.train_data_list])
                data_value = f'[{data_list_str}]'
            else:
                # 其他格式，转换为字符串
                data_value = f'"{str(self.train_data_list)}"'

            # 查找 TRAINSET_PARTIAL 字典
            pattern = r'(TRAINSET_PARTIAL\s*=\s*\{.*?)\n\}'
            match = re.search(pattern, content, re.DOTALL)

            if match:
                logger.info(f"找到 TRAINSET_PARTIAL 字典，匹配内容长度: {len(match.group(1))}")
                # 找到字典，添加新的条目
                dict_content = match.group(1)
                new_entry = f'    "{self.train_data}": {data_value},'

                # 检查是否已经存在该条目
                if f'"{self.train_data}":' not in dict_content:
                    # 在字典末尾添加新条目
                    new_dict_content = dict_content + '\n' + new_entry
                    new_content = content.replace(match.group(1) + '\n}', new_dict_content + '\n}')

                    # 写回文件
                    with open(perceptron_data_file, 'w', encoding='utf-8') as f:
                        f.write(new_content)

                    logger.info(f"已添加训练数据配置: {self.train_data} -> {data_value}")
                else:
                    logger.info(f"训练数据配置已存在: {self.train_data}")
            else:
                logger.warning("未找到 TRAINSET_PARTIAL 字典，无法更新数据配置")

        except Exception as e:
            logger.error(f"更新 Perceptron 数据配置失败: {e}")
            raise

    def _build_training_command(self) -> List[str]:
        """构建训练命令

        Returns:
            训练命令列表
        """
        # 构建 Perceptron 训练命令
        # 格式: python <exp.py> --extra_train_datasets "<train_data>:<data_mode>:<replacement_ratio>" [其他参数]

        extra_datasets = f"{self.train_data}:{self.data_mode}:{self.replacement_ratio}"

        cmd = [
            "python3",  # 使用 python3 确保支持 UTF-8
            self.exp,
            "--extra_train_datasets",
            extra_datasets
        ]

        # 添加预训练模型参数
        if self.pretrained_model:
            cmd.extend(["--pretrained_model", self.pretrained_model])

        # 添加设备参数
        cmd.extend(["-d", str(self.total_devices)])

        # 添加批次大小参数
        cmd.extend(["-b", str(self.batch_size_per_device)])

        # 添加最大轮数参数
        cmd.extend(["-e", str(self.max_epoch)])

        # 添加默认参数
        cmd.append("--find_unused_parameters")
        cmd.append("--no-clearml")

        return cmd

    def _generate_exp_name(self) -> str:
        """根据MapTRv2规则生成实验名称

        Returns:
            实验名称
        """
        # 从exp路径生成实验名称
        # 例如: perceptron/exps/end2end/private/maptrv2/0701_bsl_exp/maptrv2_exp_4v0r_con-dis_merge_br_wpre_z10_p177_fasternet_nowarp_pre_la_v2.py
        # 生成: 0701_bsl_exp__maptrv2_exp_4v0r_con-dis_merge_br_wpre_z10_p177_fasternet_nowarp_pre_la_v2

        exp_path = Path(self.exp)

        # 取路径的最后两个部分（目录名和文件名）
        if len(exp_path.parts) >= 2:
            parts = exp_path.parts[-2:]
            exp_name = "__".join(parts)
            # 去掉.py后缀
            if exp_name.endswith('.py'):
                exp_name = exp_name[:-3]
            return exp_name
        else:
            # 如果路径不够长，使用文件名
            exp_name = exp_path.stem  # 不包含扩展名的文件名
            return exp_name if exp_name else "default_exp"

    def _get_model_output_dir(self) -> str:
        """获取模型输出目录

        Returns:
            模型输出目录路径
        """
        # 获取输出根目录
        output_root_dir = os.environ.get("PERCEPTRON_OUT_DIR", "outputs")

        # 生成实验名称
        exp_name = self._generate_exp_name()

        # 使用latest目录，实际的时间戳目录由训练脚本创建
        model_output_dir = Path(output_root_dir) / exp_name / "latest" / "dump_model"

        return str(model_output_dir)
    
    def validate_config(self) -> Dict[str, Any]:
        """验证训练配置
        
        Returns:
            验证结果
        """
        result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 检查必需配置
        if not self.exp:
            result["errors"].append("缺少实验配置文件路径")
            result["is_valid"] = False
        
        if not self.ckpt:
            result["warnings"].append("未指定预训练模型路径")
        
        # 检查数值配置
        if self.max_epoch <= 0:
            result["errors"].append("最大轮数必须大于0")
            result["is_valid"] = False
        
        if self.batch_size_per_device <= 0:
            result["errors"].append("批次大小必须大于0")
            result["is_valid"] = False
        
        if self.total_devices <= 0:
            result["errors"].append("设备数量必须大于0")
            result["is_valid"] = False
        
        return result
    
    def get_training_status(self) -> Dict[str, Any]:
        """获取训练状态
        
        Returns:
            训练状态信息
        """
        # TODO: 实现训练状态查询
        return {
            "status": "unknown",
            "progress": 0.0,
            "current_epoch": 0,
            "total_epochs": self.max_epoch,
            "current_loss": 0.0,
            "estimated_time_remaining": "unknown"
        }
