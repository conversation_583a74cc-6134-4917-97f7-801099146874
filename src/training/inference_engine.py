"""推理引擎"""

import os
import subprocess
from typing import Dict, Any, Optional, List
from pathlib import Path
from utils.logger import get_logger

logger = get_logger(__name__)


class InferenceEngine:
    """推理引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config

        # 从inference配置中获取参数
        inference_config = config.get("inference", {})
        self.exp = inference_config.get("exp", "")
        self.ckpt = inference_config.get("ckpt", "")
        self.batch_size_per_device = inference_config.get("batch_size_per_device", 1)
        self.total_devices = inference_config.get("total_devices", 1)
        self.val_data = inference_config.get("val_data", "")
        self.val_data_list = inference_config.get("val_data_list", [])
        self.data_mode = inference_config.get("data_mode", "qyjpg")



        self.is_hdmap = inference_config.get("is_hdmap", False)
        self.model_path = ""  # 将从训练结果中获取或自动寻找
        self.output_dir = "outputs/inference"
    
    def run_inference(self, 
                     model_path: Optional[str] = None,
                     data_path: Optional[str] = None,
                     dry_run: bool = False) -> Dict[str, Any]:
        """执行推理
        
        Args:
            model_path: 模型路径
            data_path: 数据路径
            dry_run: 是否只生成命令不执行
            
        Returns:
            推理结果
        """
        result = {
            "success": False,
            "command": [],
            "output": "",
            "error": "",
            "inference_results": [],
            "output_dir": self.output_dir,
            "metrics": {}
        }
        
        try:
            logger.info("开始模型推理")
            logger.info(f"使用高精地图: {self.is_hdmap}")
            
            # 设置模型路径
            if model_path:
                self.model_path = model_path
            elif self.ckpt:
                # 使用配置文件中指定的模型路径
                self.model_path = self.ckpt
            else:
                # 自动寻找最新的模型
                latest_model = self._find_latest_model()
                if latest_model:
                    self.model_path = latest_model
                else:
                    logger.warning("未找到可用的模型文件，使用默认路径")
                    self.model_path = "outputs/models/trained_model.pth"
            
            logger.info(f"模型路径: {self.model_path}")
            
            # 创建输出目录
            Path(self.output_dir).mkdir(parents=True, exist_ok=True)
            
            # 构建推理命令
            inference_cmd = self._build_inference_command(data_path)
            result["command"] = inference_cmd
            
            logger.info(f"推理命令: {' '.join(inference_cmd)}")
            
            if dry_run:
                logger.info("Dry run模式，不执行实际推理")
                result["success"] = True
                result["output"] = "Dry run - 推理命令已生成"
                return result
            
            # TODO: 实现实际的推理逻辑
            # 这里是占位实现，实际应该调用具体的推理框架
            logger.info("执行推理 (占位实现)")
            
            # 模拟推理过程
            result["success"] = True
            result["output"] = "推理完成 (占位实现)"
            result["inference_results"] = [
                {
                    "input": "sample_input_1.jpg",
                    "output": "sample_output_1.json",
                    "confidence": 0.95
                },
                {
                    "input": "sample_input_2.jpg", 
                    "output": "sample_output_2.json",
                    "confidence": 0.87
                }
            ]
            result["metrics"] = {
                "total_samples": 100,
                "processed_samples": 100,
                "average_confidence": 0.91,
                "inference_time": "15m 30s",
                "throughput": "6.5 samples/sec"
            }
            
            logger.info("推理完成")
            logger.info(f"结果保存目录: {result['output_dir']}")
            logger.info(f"处理样本数: {result['metrics']['total_samples']}")
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"推理失败: {e}")
        
        return result
    
    def _build_inference_command(self, data_path: Optional[str] = None) -> List[str]:
        """构建推理命令
        
        Args:
            data_path: 数据路径
            
        Returns:
            推理命令列表
        """
        # 基础命令 - 这里使用占位命令，实际应该根据具体的推理框架构建
        cmd = [
            "python", "-m", "training.inference",
            "--model", self.model_path,
            "--output-dir", self.output_dir
        ]
        
        # 添加数据路径
        if data_path:
            cmd.extend(["--data", data_path])
        
        # 添加高精地图配置
        if self.is_hdmap:
            cmd.append("--use-hdmap")
        
        return cmd
    
    def validate_config(self) -> Dict[str, Any]:
        """验证推理配置
        
        Returns:
            验证结果
        """
        result = {
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 检查模型路径
        if self.model_path and not Path(self.model_path).exists():
            result["warnings"].append(f"模型文件不存在: {self.model_path}")
        
        # 检查输出目录权限
        try:
            Path(self.output_dir).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            result["errors"].append(f"无法创建输出目录: {e}")
            result["is_valid"] = False
        
        return result
    
    def get_inference_status(self) -> Dict[str, Any]:
        """获取推理状态
        
        Returns:
            推理状态信息
        """
        # TODO: 实现推理状态查询
        return {
            "status": "unknown",
            "progress": 0.0,
            "processed_samples": 0,
            "total_samples": 0,
            "current_throughput": 0.0,
            "estimated_time_remaining": "unknown"
        }
    
    def load_inference_results(self, results_dir: Optional[str] = None) -> List[Dict[str, Any]]:
        """加载推理结果
        
        Args:
            results_dir: 结果目录路径
            
        Returns:
            推理结果列表
        """
        if not results_dir:
            results_dir = self.output_dir
        
        results = []
        results_path = Path(results_dir)
        
        if not results_path.exists():
            logger.warning(f"推理结果目录不存在: {results_dir}")
            return results
        
        # TODO: 实现具体的结果加载逻辑
        logger.info(f"从 {results_dir} 加载推理结果 (占位实现)")
        
        return results

    def _generate_exp_name(self) -> str:
        """根据MapTRv2规则生成实验名称

        Returns:
            实验名称
        """
        # 从exp路径生成实验名称
        exp_path = Path(self.exp)

        # 取路径的最后两个部分（目录名和文件名）
        if len(exp_path.parts) >= 2:
            parts = exp_path.parts[-2:]
            exp_name = "__".join(parts)
            # 去掉.py后缀
            if exp_name.endswith('.py'):
                exp_name = exp_name[:-3]
            return exp_name
        else:
            # 如果路径不够长，使用文件名
            exp_name = exp_path.stem  # 不包含扩展名的文件名
            return exp_name if exp_name else "default_exp"

    def _find_latest_model(self) -> Optional[str]:
        """自动寻找最新的模型文件

        Returns:
            最新模型文件路径，如果没找到返回None
        """
        try:
            # 获取输出根目录
            output_root_dir = os.environ.get("PERCEPTRON_OUT_DIR", "outputs")

            # 生成实验名称
            exp_name = self._generate_exp_name()

            # 构建实验目录路径
            exp_dir = Path(output_root_dir) / exp_name

            if not exp_dir.exists():
                logger.warning(f"实验目录不存在: {exp_dir}")
                return None

            # 优先查找latest目录
            latest_dir = exp_dir / "latest"
            if latest_dir.exists() and latest_dir.is_dir():
                latest_timestamp_dir = latest_dir
                logger.info(f"使用latest目录: {latest_timestamp_dir}")
            else:
                # 如果没有latest目录，查找最新的时间戳目录
                timestamp_dirs = [d for d in exp_dir.iterdir() if d.is_dir() and d.name != "latest"]
                if not timestamp_dirs:
                    logger.warning(f"未找到时间戳目录: {exp_dir}")
                    return None

                # 按时间戳排序，取最新的
                latest_timestamp_dir = max(timestamp_dirs, key=lambda x: x.name)
                logger.info(f"使用最新时间戳目录: {latest_timestamp_dir}")

            # 查找dump_model目录中的模型文件
            dump_model_dir = latest_timestamp_dir / "dump_model"
            if not dump_model_dir.exists():
                logger.warning(f"模型目录不存在: {dump_model_dir}")
                return None

            # 查找checkpoint文件
            checkpoint_files = list(dump_model_dir.glob("checkpoint_epoch_*.pth"))
            if not checkpoint_files:
                logger.warning(f"未找到checkpoint文件: {dump_model_dir}")
                return None

            # 按epoch数排序，取最新的
            def extract_epoch(path):
                try:
                    return int(path.stem.split('_')[-1])
                except:
                    return 0

            latest_checkpoint = max(checkpoint_files, key=extract_epoch)

            logger.info(f"找到最新模型: {latest_checkpoint}")
            return str(latest_checkpoint)

        except Exception as e:
            logger.error(f"寻找最新模型失败: {e}")
            return None
