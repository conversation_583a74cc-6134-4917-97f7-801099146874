#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""自动驾驶大模型自闭环验证主命令行接口"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
src_root = project_root / "src"
sys.path.insert(0, str(src_root))

import click
import yaml
from typing import Optional, Dict, Any
from utils.logger import setup_logger, get_logger
from core.local_executor import LocalExecutor
from core.rjob_executor import RJobExecutor

logger = get_logger(__name__)


@click.command()
@click.option('--config', '-c', required=True, help='配置文件路径')
@click.option('--step', type=click.Choice(['all', 'check', 'replace', 'training', 'inference', 'eval', 'report']), 
              default='all', help='执行步骤')
@click.option('--rjob', is_flag=True, help='使用RJob执行任务')
@click.option('--dry-run', is_flag=True, help='只生成命令不执行')
@click.option('--log-level', default='INFO', help='日志级别')
@click.option('--log-file', help='日志文件名')
def main(config: str, step: str, rjob: bool, dry_run: bool, log_level: str, log_file: Optional[str]):
    """自动驾驶大模型自闭环验证 (ALV) 主命令
    
    Examples:
        # 本地执行完整流程
        alv -c config/validation_config.yaml
        
        # 本地执行训练步骤
        alv -c config/validation_config.yaml --step training
        
        # 使用RJob执行完整流程
        alv --rjob -c config/validation_config.yaml
        
        # 使用RJob执行训练步骤
        alv --rjob -c config/validation_config.yaml --step training
    """
    
    # 设置日志
    setup_logger(log_file=log_file, log_level=log_level)
    logger.info("=== 自动驾驶大模型自闭环验证 (ALV) ===")
    logger.info(f"配置文件: {config}")
    logger.info(f"执行步骤: {step}")
    logger.info(f"执行模式: {'RJob' if rjob else '本地'}")
    
    try:
        # 加载配置文件
        config_data = load_config(config)
        
        # 选择执行器
        if rjob:
            executor = RJobExecutor(config_data)
            logger.info("使用RJob执行器")
        else:
            executor = LocalExecutor(config_data)
            logger.info("使用本地执行器")
        
        # 执行任务
        result = executor.execute(step=step, dry_run=dry_run)
        
        if result["success"]:
            logger.info("任务执行成功")
            if "job_name" in result:
                logger.info(f"任务名称: {result['job_name']}")
        else:
            logger.error(f"任务执行失败: {result.get('error', '未知错误')}")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


def load_config(config_path: str) -> Dict[str, Any]:
    """加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置数据字典
    """
    try:
        config_file = Path(config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)
        
        logger.info(f"成功加载配置文件: {config_path}")
        return config_data
        
    except yaml.YAMLError as e:
        raise ValueError(f"配置文件格式错误: {e}")
    except Exception as e:
        raise RuntimeError(f"加载配置文件失败: {e}")


if __name__ == '__main__':
    main()
