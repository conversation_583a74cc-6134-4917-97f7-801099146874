"""本地执行器"""

import os
from typing import Dict, Any, Optional
from pathlib import Path
from utils.logger import get_logger
from data_checker import <PERSON><PERSON><PERSON><PERSON><PERSON>, FormatValidator
from training.trainer import Trainer
from training.inference_engine import InferenceEngine

logger = get_logger(__name__)


class LocalExecutor:
    """本地执行器 - 在本地环境直接执行验证流程"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.project_config = config.get("project", {})
        self.training_config = config.get("training", {})
        self.inference_config = config.get("inference", {})
        self.evaluation_config = config.get("evaluation", {})
        self.output_config = config.get("output", {})
        
        # 设置全局环境变量
        self._setup_environment()

        # 创建输出目录
        self._create_output_dirs()

    def _setup_environment(self):
        """设置全局环境变量"""
        import os
        env_config = self.config.get("env", {})
        for key, value in env_config.items():
            os.environ[key] = str(value)
            logger.info(f"设置全局环境变量: {key}={value}")

    def _create_output_dirs(self):
        """创建输出目录"""
        for dir_key in ["logs_dir", "reports_dir", "visualizations_dir"]:
            dir_path = self.output_config.get(dir_key, f"outputs/{dir_key.replace('_dir', '')}")
            Path(dir_path).mkdir(parents=True, exist_ok=True)
            logger.info(f"创建输出目录: {dir_path}")
    
    def execute(self, step: str = "all", dry_run: bool = False) -> Dict[str, Any]:
        """执行验证流程
        
        Args:
            step: 执行步骤
            dry_run: 是否只生成命令不执行
            
        Returns:
            执行结果
        """
        result = {
            "success": False,
            "step": step,
            "mode": "local",
            "dry_run": dry_run,
            "executed_steps": [],
            "error": None
        }
        
        try:
            logger.info(f"开始本地执行验证流程 - 步骤: {step}")
            
            if step in ['all', 'check']:
                logger.info("=== 步骤1: 数据检查 ===")
                self._execute_data_check()
                result["executed_steps"].append("check")
            
            if step in ['all', 'replace']:
                logger.info("=== 步骤2: 数据替换 ===")
                self._execute_data_replacement()
                result["executed_steps"].append("replace")
            
            if step in ['all', 'training']:
                logger.info("=== 步骤3: 模型训练 ===")
                self._execute_training(dry_run)
                result["executed_steps"].append("training")
            
            if step in ['all', 'inference']:
                logger.info("=== 步骤4: 模型推理 ===")
                self._execute_inference(dry_run)
                result["executed_steps"].append("inference")
            
            if step in ['all', 'eval']:
                logger.info("=== 步骤5: 模型评测 ===")
                self._execute_evaluation()
                result["executed_steps"].append("eval")
            
            if step in ['all', 'report']:
                logger.info("=== 步骤6: 生成报告 ===")
                self._execute_report_generation()
                result["executed_steps"].append("report")
            
            result["success"] = True
            logger.info("本地验证流程执行完成")
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"本地执行失败: {e}")
        
        return result
    
    def _execute_data_check(self):
        """执行数据检查"""
        train_data_list = self.training_config.get("train_data_list", [])
        val_data_list = self.inference_config.get("val_data_list", [])
        
        # 检查训练数据
        for data_path in train_data_list:
            logger.info(f"检查训练数据: {data_path}")
            if data_path.startswith('s3://'):
                s3_checker = S3Checker()
                result = s3_checker.check_s3_path(data_path)
                if not result["exists"]:
                    raise ValueError(f"训练数据不存在: {data_path}")
            else:
                if not Path(data_path).exists():
                    raise ValueError(f"训练数据不存在: {data_path}")
        
        # 检查验证数据
        for data_path in val_data_list:
            logger.info(f"检查验证数据: {data_path}")
            if data_path.startswith('s3://'):
                s3_checker = S3Checker()
                result = s3_checker.check_s3_path(data_path)
                if not result["exists"]:
                    raise ValueError(f"验证数据不存在: {data_path}")
            else:
                if not Path(data_path).exists():
                    raise ValueError(f"验证数据不存在: {data_path}")
        
        logger.info("数据检查完成")
    
    def _execute_data_replacement(self):
        """执行数据替换"""
        replacement_ratio = self.training_config.get("replacement_ratio", 0.08)
        logger.info(f"数据替换比例: {replacement_ratio * 100}%")
        # TODO: 实现具体的数据替换逻辑
        logger.info("数据替换功能待实现")
    
    def _execute_training(self, dry_run: bool = False):
        """执行训练"""
        # 传递完整配置给 Trainer
        trainer = Trainer(self.config)
        result = trainer.train(dry_run=dry_run)

        if not result["success"]:
            raise RuntimeError(f"训练失败: {result.get('error', '未知错误')}")

        logger.info("训练完成")
    
    def _execute_inference(self, dry_run: bool = False):
        """执行推理"""
        inference_engine = InferenceEngine(self.config)
        result = inference_engine.run_inference(dry_run=dry_run)
        
        if not result["success"]:
            raise RuntimeError(f"推理失败: {result.get('error', '未知错误')}")
        
        logger.info("推理完成")
    
    def _execute_evaluation(self):
        """执行评测"""
        # TODO: 实现评测逻辑
        logger.info("评测功能待实现")
    
    def _execute_report_generation(self):
        """生成报告"""
        # TODO: 实现报告生成逻辑
        logger.info("报告生成功能待实现")
