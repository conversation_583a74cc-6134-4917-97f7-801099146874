#!/bin/bash
set -e

# 自动驾驶大模型自闭环验证 RJob 执行脚本
# 生成时间: 2025-07-31 17:07:44
# 执行步骤: all

echo '=== 开始自动驾驶大模型自闭环验证 ==='
echo '执行环境: RJob'
echo '执行步骤: all'

# 设置环境变量
export PYTHONPATH=/workspace:$PYTHONPATH

# 进入工作目录
cd /workspace

# 安装 alv 包
echo '=== 安装 alv 包 ==='
pip install -e .

# 检查安装
echo '=== 检查 alv 命令 ==='
alv --help

# 执行验证流程
echo '=== 执行验证流程 ==='
alv -c config/validation_config.yaml

echo '=== 验证流程完成 ==='

# 保持容器运行以便查看结果
echo '任务完成，容器将保持运行状态'
tail -f /dev/null