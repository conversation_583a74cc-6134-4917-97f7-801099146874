# 自动驾驶大模型自闭环验证 (ALV) 使用指南

## 概述

ALV (Autonomous Loop Validation) 是一个自动驾驶大模型自闭环验证工具，支持本地执行和 RJob 远程执行两种模式。

## 安装

```bash
# 克隆项目
git clone <repository-url>
cd AutoLoopValidation

# 安装依赖
pip install -e .
```

安装完成后，`alv` 命令将全局可用。

## 基本用法

### 命令格式

```bash
alv [OPTIONS]
```

### 主要选项

- `-c, --config TEXT`: 配置文件路径 (必需)
- `--step [all|check|replace|training|inference|eval|report]`: 执行步骤 (默认: all)
- `--rjob`: 使用 RJob 执行任务
- `--dry-run`: 只生成命令不执行
- `--log-level TEXT`: 日志级别 (默认: INFO)
- `--log-file TEXT`: 日志文件名

## 使用示例

### 1. 本地执行完整流程

```bash
alv -c config/validation_config.yaml
```

### 2. 本地执行特定步骤

```bash
# 只执行训练步骤
alv -c config/validation_config.yaml --step training

# 只执行推理步骤
alv -c config/validation_config.yaml --step inference

# 只执行数据检查
alv -c config/validation_config.yaml --step check
```

### 3. 使用 RJob 执行

```bash
# RJob 执行完整流程
alv --rjob -c config/validation_config.yaml

# RJob 执行训练步骤
alv --rjob -c config/validation_config.yaml --step training
```

### 4. Dry Run 模式

```bash
# 本地 dry run
alv -c config/validation_config.yaml --step training --dry-run

# RJob dry run
alv --rjob -c config/validation_config.yaml --step training --dry-run
```

## 执行步骤说明

1. **check**: 数据检查 - 验证训练和验证数据的可访问性和格式
2. **replace**: 数据替换 - 按配置比例替换训练数据
3. **training**: 模型训练 - 使用新数据训练模型
4. **inference**: 模型推理 - 对验证数据进行推理
5. **eval**: 模型评测 - 评估模型性能
6. **report**: 生成报告 - 生成对比分析报告

## 配置文件

配置文件使用 YAML 格式，主要包含以下部分：

```yaml
project:
  name: "autoloop_validation"
  version: "0.1.0"

data:
  replacement_ratio: 0.08
  train_data_list: 
    - "s3://path/to/train/data.json"
  val_data_list:
    - "s3://path/to/val/data.json"

training:
  exp: "path/to/experiment/config.py"
  ckpt: "s3://path/to/checkpoint.pth"
  max_epoch: 4
  batch_size_per_device: 10

rjob:
  image: "registry.example.com/image:tag"
  namespace: "namespace"
  cpu: 32
  gpu: 2
  memory: 200000

output:
  logs_dir: "outputs/logs"
  reports_dir: "outputs/reports"
```

## 执行模式对比

### 本地执行模式

- **优点**: 直接在本地环境执行，调试方便
- **缺点**: 受本地资源限制
- **适用场景**: 开发调试、小规模验证

### RJob 执行模式

- **优点**: 利用集群资源，支持大规模计算
- **缺点**: 需要 RJob 环境支持
- **适用场景**: 生产环境、大规模训练

## 输出文件

执行完成后，结果文件将保存在以下目录：

- `outputs/logs/`: 日志文件
- `outputs/reports/`: 分析报告
- `outputs/visualizations/`: 可视化结果
- `outputs/models/`: 训练模型
- `outputs/inference/`: 推理结果
- `outputs/scripts/`: RJob 执行脚本

## 故障排除

### 常见问题

1. **模块导入错误**: 确保已正确安装包 `pip install -e .`
2. **配置文件错误**: 检查 YAML 格式和必需字段
3. **权限问题**: 确保对输出目录有写权限
4. **RJob 命令不存在**: 确保在支持 RJob 的环境中执行

### 调试技巧

1. 使用 `--dry-run` 查看生成的命令
2. 使用 `--log-level DEBUG` 获取详细日志
3. 检查 `outputs/logs/` 目录中的日志文件

## 开发说明

项目结构：

```
src/
├── core/                 # 核心模块
│   ├── cli.py           # 命令行接口
│   ├── local_executor.py # 本地执行器
│   └── rjob_executor.py  # RJob执行器
├── training/            # 训练模块
│   ├── trainer.py       # 训练器
│   ├── inference_engine.py # 推理引擎
│   └── rjob_submitter.py # RJob提交器
├── data_checker/        # 数据检查模块
└── utils/              # 工具模块
```

当前实现为占位版本，具体的训练和推理逻辑需要根据实际框架进行实现。
