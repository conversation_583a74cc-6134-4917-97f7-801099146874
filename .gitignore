perceptron/

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Virtual environment
venv/
env/
.venv/

# IDE and Editor directories
.vscode/
.idea/
*.swp
*.swo

# Build / distribution / packaging
build/
dist/
*.egg-info/
*.egg
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints/

# Environments
.env
.venv

# macOS
.DS_Store

# Logs and databases
*.log
*.sqlite

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site
