# 验证配置文件
env:
  PERCEPTRON_OUT_DIR: "output"
  NCCL_IB_TIMEOUT: 7200000
  CUDA_DEVICE_MAX_CONNECTIONS: 1
  NCCL_GID_INDEX: 3
  NCCL_ALGO: RING
  TORCH_DDP_GRAD_AS_BUCKET_VIEW: 1
  TORCH_DDP_BUCKET_CAP_MB: 64
  OMP_NUM_THREADS: 1
  MKL_NUM_THREADS: 1
  DISTRIBUTED_JOB: true

project:
  name: "autoloop_validation"
  version: "0.1.0"

rjob:
  image: "registry.qy.machdrive.cn/mach-generator/perceptron:ld-0.1"
  namespace: "mach-generator"
  charged_group: "generator_gpu"
  cpu: 32
  gpu: 2
  memory: 200000
  positive_tags: "H100"
  replica: 1
  
training:
  exp: "perceptron/exps/end2end/private/maptrv2/0701_bsl_exp/maptrv2_exp_4v0r_con-dis_merge_br_wpre_z10_p177_fasternet_nowarp_pre_la_v2.py" # z10 模型
  #exp: "perceptron/exps/end2end/private/maptrv2/0701_bsl_exp/maptrv2_exp_4v0r_con-dis_merge_br_wpre_p177_repvggb1_warp_ft_la_renori.py" # p177 模型
  pretrained_model: "s3://cjz-share/con-dis_model/ckpt_release/0726/z10_con-dis_model_0726_v0.pth"
  max_epoch: 1
  batch_size_per_device: 4
  total_devices: 1
  train_data: "whole-with-vis-attrs"  # 自闭换验证目标数据集
  train_data_list: ["s3://zqt/refresh_v5_bmk/tf-map-data-qy/data_rebuild/data_rebuild_mm_wfl_newcp_z10_fixed_calib_v2/jsons/car_z02/ppl_bag_20250114_021647_det_58333_58713.json"]
  data_mode: "qyjpg"
  replacement_ratio: 0.08  # 替换比例

inference:
  exp: "perceptron/exps/end2end/private/maptrv2/0701_bsl_exp/maptrv2_exp_4v0r_con-dis_merge_br_wpre_z10_p177_fasternet_nowarp_pre_la_v2.py" # z10 模型
  #exp: "perceptron/exps/end2end/private/maptrv2/0701_bsl_exp/maptrv2_exp_4v0r_con-dis_merge_br_wpre_p177_repvggb1_warp_ft_la_renori.py" # p177 模型
  ckpt: "s3://cjz-share/con-dis_model/ckpt_release/0726/z10_con-dis_model_0726_v0.pth"
  batch_size_per_device: 1
  total_devices: 1
  val_data: "whole-pth-bmk"  # 评测数据集
  val_data_list: "s3://liuzhengmao/tmp0523/prelabel/bmk_filtered/0523_e2e_list.json"
  data_mode: "qyjpg"

evaluation:
  ckpt: ""  # 待配置
  metrics:
    - "accuracy"
    - "precision" 
    - "recall"
    - "f1_score"

output:
  logs_dir: "outputs/logs"
  reports_dir: "outputs/reports"
  visualizations_dir: "outputs/visualizations"